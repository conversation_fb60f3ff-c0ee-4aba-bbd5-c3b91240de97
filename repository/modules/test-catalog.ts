import HttpFactory from "@/repository/factory";
import { endpoints } from "@/services/endpoints";
import { Request, Response } from "@/types";

class TestCatalogModule extends HttpFactory{

    private ORGANISMS = endpoints.organisms;
    private SPECIMENS = endpoints.specimens;
    private DEPARTMENTS = endpoints.departments;
    private TEST_TYPE_INDICATORS = `${endpoints.testTypes}/test_indicator_types`

    async getOrganisms(token: string) : Promise<Response> {
        const request : Request = {
            route: this.ORGANISMS,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }

    async getSpecimens(token: string) : Promise<Response> {
        const request : Request = {
            route: this.SPECIMENS,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }

    async getDepartments(token: string) : Promise<Response> {
        const request : Request = {
            route: this.DEPARTMENTS,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }

    async getTestTypesIndicators(token: string) : Promise<Response> {
        const request : Request = {
            route: this.TEST_TYPE_INDICATORS,
            method: 'GET',
            token: `${token}`
        }
        return this.call<Response>(request)
    }

}

export default TestCatalogModule;
