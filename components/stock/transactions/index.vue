<template>
    <div>
        <div>
            <CoreActionButton text="Transfer Stock" color="success" :icon="transferIcon" :click="init" />
        </div>

        <TransitionRoot appear :show="open" as="template">
            <Dialog as="div" @close="handleClick" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0"
                    enter-to="opacity-100" leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in"
                            leave-from="opacity-100 scale-100" leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-5xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-4 py-4 flex items-center justify-between bg-gray-50">
                                    <DialogTitle as="h3"
                                        class="text-xl text-gray-900 flex items-center font-semibold leading-6">
                                        <ArrowTopRightOnSquareIcon class="w-6 h-6 mr-3 text-blue-600" />
                                        Transfer Stock Out
                                    </DialogTitle>

                                    <button @click="handleClick"
                                        class="text-gray-400 hover:text-gray-600 transition-colors">
                                        <XMarkIcon class="w-6 h-6" />
                                    </button>
                                </div>

                                <FormKit type="form" submit-label="Update" @submit="submitForm" :actions="false"
                                    id="submitForm">

                                    <!-- Two-column layout -->
                                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 p-6">

                                        <!-- Left Column: Transfer Details -->
                                        <div class="lg:col-span-1 space-y-4">
                                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                                <h4 class="text-lg font-semibold text-blue-900 mb-4 flex items-center">
                                                    <InformationCircleIcon class="w-5 h-5 mr-2" />
                                                    Transfer Details
                                                </h4>

                                                <div class="space-y-4">
                                                    <div class="w-full flex flex-col space-y-2">
                                                        <label class="font-medium text-gray-700">Destination
                                                            Type</label>
                                                        <CoreDropdown :items="destinations"
                                                            v-model="selectedDestination" />
                                                    </div>

                                                    <div v-if="selectedDestination && selectedItems.length > 0"
                                                        class="w-full flex flex-col space-y-2">
                                                        <label class="font-medium text-gray-700">
                                                            Select {{ selectedDestination.name.toLowerCase() }}
                                                            <span class="text-red-600 font-medium">*</span>
                                                        </label>
                                                        <CoreDropdown :is-searchable="true" :items="selectedItems"
                                                            v-model="selectedItem" />
                                                    </div>

                                                    <div class="w-full flex flex-col space-y-2">
                                                        <FormKit label="Reason for transfer" type="textarea"
                                                            v-model="reason" validation="required"
                                                            placeholder="Enter the reason for this stock transfer..." />
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Transfer Summary -->
                                            <div v-if="requisitions.length > 0"
                                                class="bg-green-50 border border-green-200 rounded-lg p-4">
                                                <h4 class="text-lg font-semibold text-green-900 mb-3 flex items-center">
                                                    <ClipboardDocumentListIcon class="w-5 h-5 mr-2" />
                                                    Transfer Summary
                                                </h4>
                                                <div class="space-y-2 text-sm">
                                                    <p><span class="font-medium">Items to transfer:</span> {{
                                                        requisitions.length }}</p>
                                                    <p
                                                        v-if="selectedDestination && !selectedDestination.name.includes('select')">
                                                        <span class="font-medium">Destination:</span> {{
                                                            selectedDestination.name }}
                                                    </p>
                                                    <p v-if="selectedItem && !selectedItem.name.includes('select')">
                                                        <span class="font-medium">To:</span> {{ selectedItem.name }}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Right Column: Stock Items -->
                                        <div class="lg:col-span-2">
                                            <div class="bg-gray-50 border border-gray-200 rounded-lg">
                                                <div
                                                    class="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
                                                    <h4 class="text-lg font-semibold text-gray-900 flex items-center">
                                                        <CubeIcon class="w-5 h-5 mr-2" />
                                                        Stock Items to Transfer
                                                    </h4>
                                                    <CoreActionButton color="primary" :click="addStockItem"
                                                        text="Add Item" :icon="addIcon" size="sm" />
                                                </div>

                                                <div class="p-4">
                                                    <!-- Stock Items List -->
                                                    <div v-if="requisitions.length > 0" class="space-y-4">
                                                        <div v-for="(requisition, index) in requisitions" :key="index"
                                                            class="bg-white border border-gray-200 rounded-lg p-4">
                                                            <div
                                                                class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                                                                <div class="flex flex-col space-y-2">
                                                                    <label class="font-medium text-gray-700">Stock
                                                                        Item</label>
                                                                    <CoreDropdown :is-searchable="true"
                                                                        :items="stockItems"
                                                                        v-model="requisition.stock_item" />
                                                                </div>

                                                                <div class="flex flex-col space-y-2">
                                                                    <FormKit label="Batch Number" type="text"
                                                                        v-model="requisition.batch_number"
                                                                        placeholder="Enter batch number" />
                                                                </div>

                                                                <div class="flex flex-col space-y-2">
                                                                    <FormKit label="Lot Number" type="text"
                                                                        v-model="requisition.lot_number"
                                                                        placeholder="Enter lot number" />
                                                                </div>

                                                                <div class="flex flex-col space-y-2">
                                                                    <FormKit label="Quantity to Transfer" type="number"
                                                                        validation="required|min:1"
                                                                        v-model="requisition.quantity_requested"
                                                                        :help="requisition.stock_item.quantity ? `Available: ${requisition.stock_item.quantity}` : ''" />
                                                                </div>

                                                                <div class="flex items-end">
                                                                    <CoreActionButton :icon="deleteIcon" text="Remove"
                                                                        color="error"
                                                                        :click="(() => { deleteStockItem(index) })"
                                                                        size="sm" />
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Empty State -->
                                                    <div v-else class="text-center py-12">
                                                        <CubeIcon class="w-16 h-16 text-gray-300 mx-auto mb-4" />
                                                        <h3 class="text-lg font-medium text-gray-900 mb-2">No items
                                                            selected</h3>
                                                        <p class="text-gray-500 mb-4">Add stock items to transfer out
                                                        </p>
                                                        <CoreActionButton color="primary" :click="addStockItem"
                                                            text="Add First Item" :icon="addIcon" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div
                                        class="mt-6 justify-end flex items-center space-x-3 px-6 py-4 border-t bg-gray-50">
                                        <CoreOutlinedButton text="Clear Form" :click="(() => clearForm())" />
                                        <CoreActionButton color="success" :icon="saveIcon" text="Process Transfer"
                                            :disabled="!isFormValid" />
                                    </div>
                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'

import { TrashIcon, PlusIcon, XMarkIcon, UserIcon, ArrowDownTrayIcon, ArrowTopRightOnSquareIcon, ArrowUturnLeftIcon, InformationCircleIcon, ClipboardDocumentListIcon, CubeIcon } from '@heroicons/vue/24/solid/index.js'
import type { Department, Request, RequisitionItem, Response, Ward } from '@/types';
import StockModule from '@/repository/modules/stock';
import { endpoints } from '@/services/endpoints';
import fetchRequest from '@/services/fetch';

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon,
        UserIcon
    },
    data() {

        return {
            open: false as boolean,
            transferIcon: ArrowTopRightOnSquareIcon,
            saveIcon: ArrowDownTrayIcon,
            clearIcon: ArrowUturnLeftIcon,
            addIcon: PlusIcon,
            deleteIcon: TrashIcon,
            destinations: new Array<{ name: string }>(),
            selectedDestination: { name: '-- select destination --' } as { name: string },
            reason: '' as string,
            requisitions: new Array<RequisitionItem>(),
            stockItems: new Array<any>(),
            cookie: useCookie('token'),
            showFacility: false as boolean,
            showWard: false as boolean,
            showDepartment: false as boolean,
            departments: new Array<Department>(),
            selectedDeparment: { name: '-- select department --' } as Department,
            facilities: new Array<any>(),
            selectedFacility: { name: '-- select facility --' },
            wards: new Array<Ward>(),
            selectedWard: { name: '-- select Ward --' } as Ward,
            selectedItem: { name: '-- select item --' }
        }
    },
    computed: {
        destinationOptions() {
            return [
                { name: 'Department', label: 'Department' },
                { name: 'Facility', label: 'Facility' },
                { name: 'Wards', label: 'Wards' }
            ];
        },
        selectedItems() {
            switch (this.selectedDestination.name) {
                case 'Department':
                    this.selectedItem = { name: '-- select department --' }
                    return this.departments;
                case 'Facility':
                    this.selectedItem = { name: '-- select facility --' }
                    return this.facilities;
                case 'Ward':
                    this.selectedItem = { name: '-- select ward --' }
                    return this.wards;
                default:
                    return [];
            }
        },
        isFormValid() {
            // Check if basic transfer details are filled
            if (!this.selectedDestination || this.selectedDestination.name.includes('select')) {
                return false;
            }

            if (!this.selectedItem || this.selectedItem.name.includes('select')) {
                return false;
            }

            if (!this.reason || this.reason.trim() === '') {
                return false;
            }

            // Check if at least one stock item is added
            if (this.requisitions.length === 0) {
                return false;
            }

            // Check if all stock items are properly filled
            const hasInvalidRequisition = this.requisitions.some((requisition) => {
                return (
                    !requisition.stock_item.name ||
                    requisition.stock_item.name.includes('select') ||
                    requisition.quantity_requested <= 0
                );
            });

            return !hasInvalidRequisition;
        }
    },
    methods: {
        async init(): Promise<void> {
            await this.loadStockItems();
            this.addDestinations();
            this.handleClick();
        },
        async submitForm(): Promise<void> {
            if (!this.isFormValid) {
                useNuxtApp().$toast.error('Please fill in all required fields');
                return;
            }

            try {
                const stockModule = new StockModule();
                const sendingTo = `${this.selectedItem.name} ${this.selectedDestination.name}`;

                const stockItems = this.requisitions.map((requisition) => ({
                    stock_item_id: requisition.stock_item.id,
                    quantity: requisition.quantity_requested,
                    lot: requisition.lot_number || '',
                    batch: requisition.batch_number || ''
                }));

                const params = {
                    sending_to: sendingTo,
                    stock_status_reason: this.reason,
                    stock_items: stockItems
                };

                const { data, error } = await stockModule.stockOutTransaction(`${this.cookie}`, params);

                if (data.value) {
                    useNuxtApp().$toast.success('Stock transfer processed successfully');
                    this.clearForm();
                    this.handleClick();
                    // Emit event to parent component to refresh data
                    this.$emit('transfer-completed');
                }

                if (error.value) {
                    console.error(error.value);
                    useNuxtApp().$toast.error('Failed to process stock transfer. Please try again.');
                }
            } catch (error) {
                console.error('Transfer error:', error);
                useNuxtApp().$toast.error('An error occurred while processing the transfer');
            }
        },
        async loadStockItems(): Promise<void> {
            const stockModule = new StockModule();
            const { data, error } = await stockModule.getStockItem(`${this.cookie}`, "metadata=true")
            if (data.value) {
                this.stockItems = data.value
            }
            if (error.value) {
                console.error(error.value)
                useNuxtApp().$toast.error('Failed to load stock items');
            }
        },
        async loadDepartments(): Promise<void> {
            const request: Request = {
                route: endpoints.departments,
                method: "GET",
                token: `${this.cookie}`
            }
            const { data, error }: Response = await fetchRequest(request);
            if (data.value) {
                this.departments = data.value
            }
            if (error.value) {
                console.error(error.value)
            }
        },
        async loadFacilities(): Promise<void> {
            const request: Request = {
                route: endpoints.facility,
                method: "GET",
                token: `${this.cookie}`
            }
            const { data, error }: Response = await fetchRequest(request);
            if (data.value) {
                this.facilities = data.value.data
            }
            if (error.value) {
                console.error(error.value)
            }
        },
        async loadWards(): Promise<void> {
            console.log("bjkm")
            const request: Request = {
                route: 'encounter_type_facility_section_mappings/facility_sections?encounter_type_id=2',
                method: "GET",
                token: `${this.cookie}`
            }
            const { data, error }: Response = await fetchRequest(request);
            if (data.value) {
                this.wards = data.value
            }
            if (error.value) {
                console.error(error.value)
            }
        },
        addDestinations(): void {
            let destinations = ['Facility', 'Ward', 'Department'];
            destinations.forEach((destination: string) => {
                this.destinations.push({ name: destination })
            });
        },
        addStockItem(): void {
            this.requisitions.push({
                id: Date.now(), // Use timestamp as temporary ID
                stock_item: { name: '-- select item --', id: 0 },
                quantity_requested: 0,
                batch_number: '',
                lot_number: '',
                expiry_date: ''
            })
        },
        deleteStockItem(index: number): void {
            if (index >= 0 && index < this.requisitions.length) {
                this.requisitions.splice(index, 1);
            }
        },
        clearForm(): void {
            this.$formkit.reset('submitForm');
            this.selectedDestination = { name: '-- select destination --' };
            this.selectedItem = { name: '-- select item --' };
            this.reason = '';
            this.requisitions = [];
        },
        async checkStockQuantity(requisition: RequisitionItem): Promise<void> {
            if (!requisition.stock_item.id || !requisition.quantity_requested || !requisition.batch_number || !requisition.lot_number) {
                return;
            }

            const stockModule = new StockModule();
            const params = {
                stock_item_id: requisition.stock_item.id,
                quantity: requisition.quantity_requested,
                batch: requisition.batch_number,
                lot: requisition.lot_number
            };

            try {
                const { data, error } = await stockModule.checkStockQuantity(`${this.cookie}`, params);

                if (data.value) {
                    if (!data.value.deduction_allowed) {
                        useNuxtApp().$toast.warning(data.value.message);
                        requisition.quantity_requested = 0;
                    }
                    requisition.stock_item.quantity = data.value.available_quantity || 0;
                }

                if (error.value) {
                    console.error(error.value);
                    useNuxtApp().$toast.error("Could not verify stock quantity for given lot and batch");
                }
            } catch (error) {
                console.error('Stock quantity check error:', error);
            }
        },
        handleClick() {
            this.open = !this.open
        },

    },
    watch: {
        selectedDestination: {
            handler(value: { name: string }) {
                const destinations: {
                    [key: string]: () => Promise<void>;
                } = {
                    'Department': this.loadDepartments,
                    'Facility': this.loadFacilities,
                    'Ward': this.loadWards
                };

                const loadFunction = destinations[value.name];
                if (loadFunction) {
                    loadFunction.call(this);
                }
            },
            deep: true
        },
        requisitions: {
            handler(newRequisitions: RequisitionItem[]) {
                newRequisitions.forEach((requisition) => {
                    // Update stock item ID when stock item name changes
                    if (requisition.stock_item.name && !requisition.stock_item.name.includes('select')) {
                        const foundItem = this.stockItems.find((item: any) => item.name === requisition.stock_item.name);
                        if (foundItem) {
                            requisition.stock_item.id = foundItem.id;
                        }
                    }

                    // Check stock quantity when all required fields are filled
                    if (requisition.stock_item.id && requisition.quantity_requested > 0 &&
                        requisition.batch_number && requisition.lot_number) {
                        this.checkStockQuantity(requisition);
                    }
                });
            },
            deep: true
        }
    }
}
</script>

<style></style>
