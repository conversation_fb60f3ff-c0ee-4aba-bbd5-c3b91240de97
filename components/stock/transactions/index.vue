<template>
    <div>

        <div>
            <CoreActionButton text="Transfer" color="success" :icon="transferIcon" :click="init" />
        </div>

        <TransitionRoot appear :show="open" as="template">
            <Dialog as="div" @close="handleClick" class="relative z-10">
                <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
                    leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="fixed inset-0 bg-black bg-opacity-25"></div>
                </TransitionChild>

                <div class="fixed inset-0 overflow-y-auto">
                    <div class="flex min-h-full items-center justify-center p-4 text-center">
                        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
                            enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
                            leave-to="opacity-0 scale-95">
                            <DialogPanel
                                class="w-full max-w-3xl transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">

                                <div class="border-b px-3 py-3 flex items-center justify-between">

                                    <DialogTitle as="h3" class="text-xl text-black flex items-center font-medium leading-6">
                                        <img src="~assets/icons/ambulance.svg" class="w-8 h-8 mr-2" />
                                        Transfer Stock
                                    </DialogTitle>

                                    <button @click="handleClick">
                                        <XMarkIcon class="w-5 h-5" />
                                    </button>

                                </div>

                                <FormKit type="form" submit-label="Update" @submit="submitForm" :actions="false"
                                    #default="{ value }" id="submitForm">
                                    <div class="w-full mt-2 space-y-3 px-5">

                                        <div class="w-full flex flex-col space-y-2">
                                            <label class="font-medium">Destination</label>
                                            <CoreDropdown :items="destinations" v-model="selectedDestination" />
                                        </div>

                                        <div v-if="selectedDestination && selectedItems.length > 0"
                                            class="w-full flex flex-col space-y-2">
                                            <label class="font-medium">Select {{ selectedDestination.name.toLowerCase() }}<span class="text-red-600 font-medium">*</span></label>
                                            <CoreDropdown :is-searchable="true" :items="selectedItems" v-model="selectedItem" />
                                        </div>

                                        <FormKit label="Reason for transfer" type="textarea" v-model="reason"
                                            validation="required" />

                                        <CoreActionButton color="primary" :click="addStockItem" text="Add items"
                                            :icon="addIcon" />

                                        <div class="flex flex-col space-y-3 mb-5"
                                            v-for="(requisition, index) in requisitions" :key="index">
                                            <div class="grid grid-cols-4 gap-4 mb-5">
                                                <div class="flex flex-col space-y-2">
                                                    <label class="font-medium mb-0">Stock item</label>
                                                    <CoreDropdown :is-searchable="true" :items="stockItems"
                                                        v-model="requisition.stock_item" />
                                                </div>
                                                <FormKit label="Quantity requested" type="number" validation="required"
                                                    v-model="requisition.quantity_requested" />
                                                <div class="mt-8">
                                                    <CoreActionButton :icon="deleteIcon" text="Delete" color="error"
                                                        :click="(() => { deleteStockItem(index) })" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-4 justify-end flex items-center space-x-3 px-3 py-2 border-t">
                                        <CoreOutlinedButton text="Clear form" :click="(() => clearForm())" />
                                        <CoreActionButton color="success" :icon="saveIcon" text="Save changes" />
                                    </div>
                                </FormKit>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </Dialog>
        </TransitionRoot>
    </div>
</template>

<script lang="ts">

import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
} from '@headlessui/vue'

import { TrashIcon, PlusIcon, XMarkIcon, UserIcon, ArrowDownTrayIcon, ArrowTopRightOnSquareIcon, ArrowUturnLeftIcon } from '@heroicons/vue/24/solid/index.js'
import type { Department, Request, RequisitionItem, Response, Ward } from '@/types';
import StockModule from '@/repository/modules/stock';
import { endpoints } from '@/services/endpoints';
import fetchRequest from '@/services/fetch';

export default {
    components: {
        TransitionRoot,
        TransitionChild,
        Dialog,
        DialogPanel,
        DialogTitle,
        XMarkIcon,
        UserIcon
    },
    data() {

        return {
            open: false as boolean,
            transferIcon: ArrowTopRightOnSquareIcon,
            saveIcon: ArrowDownTrayIcon,
            clearIcon: ArrowUturnLeftIcon,
            addIcon: PlusIcon,
            deleteIcon: TrashIcon,
            destinations: new Array<{ name: string }>(),
            selectedDestination: { name: '-- select destination --' } as { name: string },
            reason: '' as string,
            requisitions: new Array<RequisitionItem>(),
            stockItems: new Array<any>(),
            cookie: useCookie('token'),
            showFacility: false as boolean,
            showWard: false as boolean,
            showDepartment: false as boolean,
            departments: new Array<Department>(),
            selectedDeparment: { name: '-- select department --' } as Department,
            facilities: new Array<any>(),
            selectedFacility: { name: '-- select facility --' },
            wards: new Array<Ward>(),
            selectedWard: { name: '-- select Ward --' } as Ward,
            selectedItem: { name: '-- select item --' }
        }
    },
    computed: {
        destinationOptions() {
            return [
                { name: 'Department', label: 'Department' },
                { name: 'Facility', label: 'Facility' },
                { name: 'Wards', label: 'Wards' }
            ];
        },
        selectedItems() {
            switch (this.selectedDestination.name) {
                case 'Department':
                    this.selectedItem = { name: '-- select department --'}
                    return this.departments;
                case 'Facility':
                    this.selectedItem = { name: '-- select facility --'}
                    return this.facilities;
                case 'Ward':
                    this.selectedItem = { name: '-- select ward --'}
                    return this.wards;
                default:
                    return [];
            }
        }
    },
    methods: {
        async init(): Promise<void> {
            await this.loadStockItems();
            this.addDestinations();
            this.handleClick();
        },
        submitForm(): void {

        },
        async loadStockItems(): Promise<void> {
            const stockModule = new StockModule();
            const { data, error } = await stockModule.getStockItem(`${this.cookie}`)
            if (data.value) {
                this.stockItems = data.value
            }
            if (error.value) {
                console.error(error.value)
            }
        },
        async loadDepartments(): Promise<void> {
            const request: Request = {
                route: endpoints.departments,
                method: "GET",
                token: `${this.cookie}`
            }
            const { data, error }: Response = await fetchRequest(request);
            if (data.value) {
                this.departments = data.value
            }
            if (error.value) {
                console.error(error.value)
            }
        },
        async loadFacilities(): Promise<void> {
            const request: Request = {
                route: endpoints.facility,
                method: "GET",
                token: `${this.cookie}`
            }
            const { data, error }: Response = await fetchRequest(request);
            if (data.value) {
                this.facilities = data.value.data
            }
            if (error.value) {
                console.error(error.value)
            }
        },
        async loadWards(): Promise<void> {
            console.log("bjkm")
            const request: Request = {
                route: 'encounter_type_facility_section_mappings/facility_sections?encounter_type_id=2',
                method: "GET",
                token: `${this.cookie}`
            }
            const { data, error }: Response = await fetchRequest(request);
            if (data.value) {
                this.wards = data.value
            }
            if (error.value) {
                console.error(error.value)
            }
        },
        addDestinations(): void {
            let destinations = ['Facility', 'Ward', 'Department'];
            destinations.forEach((destination: string) => {
                this.destinations.push({ name: destination })
            });
        },
        addStockItem(): void {
            this.requisitions.push({
                stock_item: { name: '-- select item --', id: 0 },
                quantity_requested: 0
            })
        },
        deleteStockItem(index: number): void {
            if (index >= 0 && index < this.requisitions.length) {
                this.requisitions.splice(index, 1);
            }
        },
        clearForm(): void {
            this.$formkit.reset('submitForm');
        },
        handleClick() {
            this.open = !this.open
        },

    },
    watch: {
        selectedDestination: {
            handler(value: { name: string }) {
                const destinations: {
                    [key: string]: () => Promise<void>;
                } = {
                    'Department': this.loadDepartments,
                    'Facility': this.loadFacilities,
                    'Ward': this.loadWards
                };

                const loadFunction = destinations[value.name];
                if (loadFunction) {
                    loadFunction.call(this);
                }
            },
            deep: true
        }
    }
}
</script>

<style>
</style>
