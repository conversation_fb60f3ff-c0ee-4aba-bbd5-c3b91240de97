import { ref, computed } from "vue";
import { useAuthStore } from "@/store/auth";

export function usePermissions() {

  const authStore = useAuthStore();
  const permissionMap: Ref<Map<string, boolean>> = ref(
    new Map<string, boolean>()
  );

  const initializePermissions = (): void => {
    const userPermissions = authStore.user.permissions || [];
    userPermissions.forEach((permission) => {
      permissionMap.value.set(permission.name.toLowerCase(), true);
    });
  };

  const hasPermission = computed(
    () => (permission: string) =>
      permissionMap.value.has(permission.toLowerCase())
  );

  type PermissionFunction = (resource: string) => boolean;

  interface PermissionObject {
    view: PermissionFunction;
    create: PermissionFunction;
    edit: PermissionFunction;
    delete: PermissionFunction;
    manage: PermissionFunction;
    refer: PermissionFunction;
    request: PermissionFunction;
    accept: PermissionFunction;
    reject: PermissionFunction;
    start: PermissionFunction;
    enter: PermissionFunction;
    verify: PermissionFunction;
    ignore: PermissionFunction;
    void: PermissionFunction;
  }

  const can: PermissionObject = {
    view: (resource: string) => hasPermission.value(`view_${resource}`),
    create: (resource: string) => hasPermission.value(`create_${resource}`),
    edit: (resource: string) => hasPermission.value(`edit_${resource}`),
    delete: (resource: string) => hasPermission.value(`delete_${resource}`),
    manage: (resource: string) => hasPermission.value(`manage_${resource}`),
    refer: (resource: string) => hasPermission.value(`refer_${resource}`),
    request: (resource: string) => hasPermission.value(`request_${resource}`),
    accept: (resource: string) => hasPermission.value(`accept_${resource}`),
    reject: (resource: string) => hasPermission.value(`reject_${resource}`),
    start: (resource: string) => hasPermission.value(`start_${resource}`),
    enter: (resource: string) => hasPermission.value(`enter_${resource}`),
    verify: (resource: string) => hasPermission.value(`verify_${resource}`),
    ignore: (resource: string) => hasPermission.value(`ignore_${resource}`),
    void: (resource: string) => hasPermission.value(`void_${resource}`),
  };

  const allowOperation = (operation: string, resource: string) => {
    const permissionKey = `${operation}_${resource}`.toLowerCase();
    return hasPermission.value(permissionKey);
  };

  initializePermissions();
  
  return {
    can,
    hasPermission,
    allowOperation,
    initializePermissions,
  };
}
