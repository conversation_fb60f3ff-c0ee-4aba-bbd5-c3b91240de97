<template>
    <div class="px-5 py-5">
        <CoreBreadcrumb :pages="pages" />
        <div class="flex items-center justify-between py-5">
            <h3 class="text-2xl font-semibold capitalize">Transfer Stock</h3>
            <div class="">
                <StockTransactionsCheckout :disable-checkout="validateTransaction"
                    :data="{ reason: reason, sendingTo: `${selectedItem.name.includes('select') ? '' : selectedItem.name} ${!selectedDestination.name.includes('select') ? selectedDestination.name : ''}`, requisitions: requisitions }" />
            </div>
        </div>

        <div>
            <div class="w-full mt-2 grid grid-cols-3 gap-2">
                <div class="col-span-1 order-2 rounded border">
                    <div class="bg-gray-50 px-2 py-2 border-b rounded-t">
                        <h3 class="font-semibold text-lg">Details</h3>
                    </div>
                    <div class="flex flex-col space-y-2 px-5 py-5">
                        <div class="w-full flex flex-col space-y-2">
                            <label class="font-medium">Destination</label>
                            <CoreDropdown :items="destinations" v-model="selectedDestination" />
                        </div>

                        <div v-if="selectedDestination && selectedItems.length > 0"
                            class="w-full flex flex-col space-y-2">
                            <label class="font-medium">Select {{ selectedDestination.name.toLowerCase() }}<span
                                    class="text-red-600 font-medium">*</span></label>
                            <CoreDropdown :is-searchable="true" :items="selectedItems" v-model="selectedItem" />
                        </div>

                        <FormKit label="Reason for transfer" type="textarea" v-model="reason" validation="required" />
                    </div>
                </div>

                <div class="col-span-2 order-1 rounded border">
                    <div class="bg-gray-50 px-2 py-2 border-b rounded-t">
                        <h3 class="font-semibold text-lg">Stock Items</h3>
                    </div>
                    <div class="px-5 py-5">
                        <CoreActionButton v-show="requisitions.length > 0" color="primary" :click="addStockItem"
                            text="Add items" :icon="addIcon" />

                        <div class="flex flex-col space-y-3 mt-5" v-for="(requisition, index) in requisitions"
                            :key="index">
                            <div class="grid grid-cols-5 gap-4 mb-5">
                                <CoreMultiselect label="Stock item" mode="single" :items="stockItems.map((s) => s.name)"
                                    v-model:items-selected="requisition.stock_item.name" />

                                <CoreMultiselect label="Batch" mode="single"
                                    :items="computedBatchNumbers(requisition.stock_item.name).value"
                                    v-model:items-selected="requisition.batch_number" :delay="5" />

                                <CoreMultiselect label="Lot" mode="single"
                                    :items="computedLotNumbers(requisition.stock_item.name).value"
                                    v-model:items-selected="requisition.lot_number" />

                                <FormKit label="Quantity requested" type="number" validation="required"
                                    v-model.lazy="requisition.quantity_requested" :delay="1000"
                                    :help="`Available quantity: ${requisition.stock_item.quantity}`" />

                                <div class="mt-8 flex items-center space-x-2">
                                    <CoreLoader :height="20" :width="20"
                                        v-if="requisitionLoading.id == requisition.id && requisitionLoading.value" />
                                    <CoreActionButton type="button" v-if="!requisitionLoading.value" :icon="deleteIcon"
                                        text="Delete" color="error" :click="(() => { deleteStockItem(index) })" />
                                </div>
                            </div>
                        </div>
                        <div v-show="requisitions.length == 0"
                            class="flex flex-col items-center justify-center space-y-2">
                            <img src="@/assets/icons/stock_out.svg" class="w-28 h-28 text-red-500"
                                alt="stock-out-svg" />
                            <p class="flex items-center">No stock items for transfer</p>
                            <button @click="addStockItem" type="button"
                                class="flex items-center text-sky-500 font-medium">
                                <SquaresPlusIcon class="w-5 h-5 mr-2" />
                                Add stock item
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { TrashIcon, PlusIcon } from '@heroicons/vue/24/solid/index.js';
import { SquaresPlusIcon } from '@heroicons/vue/20/solid';
import type { Department, Request, RequisitionItem, Response, Ward, Page, DropdownItem, Facility, StockItem } from '@/types';
import StockModule from '@/repository/modules/stock';
import { endpoints } from '@/services/endpoints';
import fetchRequest from '@/services/fetch';
import Package from '@/package.json';

definePageMeta({
    layout: 'dashboard'
})

useHead({
    title: `${Package.name.toUpperCase()} - Transfer Stock`
})

const addIcon = PlusIcon
const deleteIcon = TrashIcon
const { $toast, $metadata } = useNuxtApp();
const destinations = ref<DropdownItem[]>([])
const selectedDestination = ref<DropdownItem>({ name: 'select destination' })
const reason = ref<string>('')
const requisitions = ref<RequisitionItem[]>([])
const stockItems = ref<StockItem[]>([])
const cookie = useCookie('token')
const departments = ref<Department[]>([])
const facilities = ref<Facility[]>([])
const wards = ref<Ward[]>([])
const selectedItem = ref<DropdownItem>({ name: 'select item' })
const pages = ref<Page>([
    { name: 'Home', link: '/home' },
    { name: 'Stock Management', link: '#' },
    { name: 'Transactions', link: '/stock-management/transactions' }
])
const requisitionLoading = ref<{ id: number, value: boolean }>({ id: 0, value: false })
const selectedItems: ComputedRef<any[]> = computed(() => {
    const optionsMap = {
        Department: { items: departments, placeholder: 'select department' },
        Facility: { items: facilities, placeholder: 'select facility' },
        Ward: { items: wards, placeholder: 'select ward' }
    } as const;
    const destination = optionsMap[selectedDestination.value.name as keyof typeof optionsMap];
    if (destination) {
        selectedItem.value = { name: destination.placeholder };
        return destination.items.value;
    }
    return [];
});

const validateTransaction = computed((): boolean => {
    if (requisitions.value.length === 0) {
        return true;
    }

    if (!selectedDestination.value || selectedDestination.value.name.includes('select')) {
        return true;
    }

    if (!selectedItem.value || selectedItem.value.name.includes('select')) {
        return true;
    }

    if (!reason.value || reason.value.trim() === '') {
        return true;
    }

    const hasInvalidRequisition = requisitions.value.some((requisition) => {
        return (
            !requisition.stock_item.name ||
            requisition.stock_item.name.includes('select') ||
            requisition.quantity_requested <= 0 ||
            !requisition.batch_number ||
            !requisition.lot_number
        );
    });

    return hasInvalidRequisition;
});

const computedLotNumbers = (selectedName: string) => computed((): string[] | undefined => {
    const foundItem = stockItems.value.find((item) => item.name === selectedName);
    return foundItem ? foundItem.lots : [];
});

const computedBatchNumbers = (selectedName: string) => computed((): string[] | undefined => {
    const foundItem = stockItems.value.find((item) => item.name === selectedName);
    return foundItem ? foundItem.batches : [];
});

async function init(): Promise<void> {
    loadStockItems()
    addDestinations()
}

async function loadStockItems(): Promise<void> {
    const stockModule = new StockModule()
    const { data, error } = await stockModule.getStockItem(String(cookie.value), "metadata=true")
    if (data.value) {
        stockItems.value = data.value
    }
    if (error.value) {
        console.error(error.value)
    }
}

async function loadFacilities(): Promise<void> {
    const request: Request = {
        route: endpoints.facility,
        method: "GET",
        token: cookie.value as string
    }
    const { data, error }: Response = await fetchRequest(request)
    if (data.value) {
        facilities.value = data.value.data
    }
    if (error.value) {
        console.error(error.value)
    }
}

async function loadWards(): Promise<void> {
    const request: Request = {
        route: 'encounter_type_facility_section_mappings/facility_sections?encounter_type_id=2',
        method: "GET",
        token: cookie.value as string
    }
    const { data, error }: Response = await fetchRequest(request)
    if (data.value) {
        wards.value = data.value
    }
    if (error.value) {
        console.error(error.value)
    }
}

function addDestinations(): void {
    let destinationList: string[] = ['Facility', 'Ward', 'Department']
    destinations.value = destinationList.map(destination => ({ name: destination }))
}

async function checkStockQuantity(requisition: RequisitionItem): Promise<void> {
    requisitionLoading.value = { id: requisition.id, value: true }
    const stockModule = new StockModule()
    const params = {
        stock_item_id: stockItems.value.find((item) => (item.name == requisition.stock_item.name))?.id || 0,
        quantity: requisition.quantity_requested,
        batch: requisition.batch_number,
        lot: requisition.lot_number
    }
    const { data, error, pending }: Response = await stockModule.checkStockQuantity(cookie.value as string, params)
    requisitionLoading.value = { id: requisition.id, value: pending }
    if (data.value) {
        if (!data.value.deduction_allowed) {
            useNuxtApp().$toast.warning(data.value.message)
            requisition.quantity_requested = 0
        }
        data.value.available_quantity !== null ? (requisition.stock_item.quantity = data.value.available_quantity) : requisition.stock_item.quantity = 0;
        requisitionLoading.value = { id: requisition.id, value: false }
    }
    if (error.value) {
        console.error(error.value)
        requisitionLoading.value = { id: requisition.id, value: false }
        $toast.error("Could not verify stock quantity for given lot and batch, please try again")
    }
}

function addStockItem(): void {
    requisitions.value.push({
        id: 0,
        stock_item: { name: 'select item', id: 0, quantity: 0 },
        quantity_requested: 0,
        batch_number: '',
        lot_number: '',
    })
}

function deleteStockItem(index: number): void {
    if (index >= 0 && index < requisitions.value.length) {
        requisitions.value.splice(index, 1)
    }
}

onMounted(() => {
    init()
})

watch(selectedDestination, (value: { name: string }) => {
    const destinations: {
        [key: string]: () => Promise<void>
    } = {
        'Department': (async () => { departments.value = $metadata.departments }),
        'Facility': loadFacilities,
        'Ward': loadWards
    }

    const loadFunction = destinations[value.name]
    if (loadFunction) {
        loadFunction()
    }
}, { deep: true })

watch(requisitions, (oldRequisitions: RequisitionItem[], newRequisitions: RequisitionItem[]) => {
    const oldStockItems = oldRequisitions.map((requisition: RequisitionItem) => requisition.stock_item.name);
    const newStockItems = newRequisitions.map((requisition: RequisitionItem) => requisition.stock_item.name);
    if (oldStockItems !== newStockItems) {
        newRequisitions.forEach((requisition) => {
            requisition.stock_item.id = stockItems.value.find((item) => item.name === requisition.stock_item.name)?.id || 0;
            requisition.stock_item.id && checkStockQuantity(requisition);
        });
    }
}, { deep: true });
</script>
<style scoped></style>