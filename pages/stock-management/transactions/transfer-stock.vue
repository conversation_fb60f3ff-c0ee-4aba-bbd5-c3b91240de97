<template>
    <div class="px-5 py-5">
        <CoreBreadcrumb :pages="pages" />
        <div class="flex items-center justify-between py-5">
            <h3 class="text-2xl font-semibold capitalize">Transfer Stock</h3>
            <div class="flex items-center space-x-3">
                <CoreOutlinedButton text="Clear Form" :click="clearForm"
                    v-if="requisitions.length > 0 || reason.trim() !== ''" />
                <StockTransactionsCheckout :disable-checkout="!canSubmit"
                    :data="{ reason: reason, sendingTo: `${selectedItem.name.includes('select') ? '' : selectedItem.name} ${!selectedDestination.name.includes('select') ? selectedDestination.name : ''}`, requisitions: requisitions }" />
            </div>
        </div>

        <div>
            <div class="w-full mt-2 grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Transfer Details Section -->
                <div class="lg:col-span-1 order-2 lg:order-2">
                    <div class="bg-gray-50 border border-gray-200 rounded-lg">
                        <div class="bg-gray-100 px-4 py-3 border-b border-gray-200 rounded-t-lg">
                            <h3 class="font-semibold text-lg text-gray-800 flex items-center">
                                <InformationCircleIcon class="w-5 h-5 mr-2 text-blue-600" />
                                Transfer Details
                            </h3>
                        </div>
                        <div class="flex flex-col space-y-4 px-5 py-5">
                            <div class="w-full flex flex-col space-y-2">
                                <label class="font-medium text-gray-700">Destination Type</label>
                                <CoreDropdown :items="destinations" v-model="selectedDestination" />
                            </div>

                            <div v-if="selectedDestination && selectedItems.length > 0"
                                class="w-full flex flex-col space-y-2">
                                <label class="font-medium text-gray-700">
                                    Select {{ selectedDestination.name.toLowerCase() }}
                                    <span class="text-red-600 font-medium">*</span>
                                </label>
                                <CoreDropdown :is-searchable="true" :items="selectedItems" v-model="selectedItem" />
                            </div>

                            <div class="w-full flex flex-col space-y-2">
                                <FormKit label="Reason for transfer" type="textarea" v-model="reason"
                                    validation="required" placeholder="Enter the reason for this stock transfer..." />
                            </div>
                        </div>
                    </div>

                    <!-- Transfer Summary -->
                    <div v-if="requisitions.length > 0" class="mt-4 bg-green-50 border border-green-200 rounded-lg">
                        <div class="bg-green-100 px-4 py-3 border-b border-green-200 rounded-t-lg">
                            <h4 class="font-semibold text-lg text-green-800 flex items-center">
                                <ClipboardDocumentListIcon class="w-5 h-5 mr-2" />
                                Transfer Summary
                            </h4>
                        </div>
                        <div class="px-4 py-4 space-y-2 text-sm text-green-700">
                            <div class="flex justify-between">
                                <span class="font-medium">Items to transfer:</span>
                                <span class="font-semibold">{{ requisitions.length }}</span>
                            </div>
                            <div v-if="selectedDestination && !selectedDestination.name.includes('select')"
                                class="flex justify-between">
                                <span class="font-medium">Destination:</span>
                                <span class="font-semibold">{{ selectedDestination.name }}</span>
                            </div>
                            <div v-if="selectedItem && !selectedItem.name.includes('select')"
                                class="flex justify-between">
                                <span class="font-medium">To:</span>
                                <span class="font-semibold">{{ selectedItem.name }}</span>
                            </div>
                            <div v-if="reason" class="flex justify-between">
                                <span class="font-medium">Reason:</span>
                                <span class="font-semibold">{{ reason.substring(0, 50) }}{{ reason.length > 50 ? '...' :
                                    '' }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Stock Items Section -->
                <div class="lg:col-span-3 order-1 lg:order-1">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg">
                        <div
                            class="bg-blue-100 px-4 py-3 border-b border-blue-200 rounded-t-lg flex items-center justify-between">
                            <h3 class="font-semibold text-lg text-blue-800 flex items-center">
                                <CubeIcon class="w-5 h-5 mr-2" />
                                Items to Transfer
                            </h3>
                            <CoreActionButton color="primary" :click="addStockItem" text="Add Item" :icon="addIcon"
                                size="sm" />
                        </div>
                        <div class="px-5 py-5">
                            <!-- Stock Items List -->
                            <div v-if="requisitions.length > 0" class="space-y-4">
                                <div v-for="(requisition, index) in requisitions" :key="index"
                                    class="bg-white border border-blue-200 rounded-lg p-4 shadow-sm">
                                    <!-- Item Header -->
                                    <div class="flex items-center justify-between mb-4 pb-2 border-b border-gray-200">
                                        <h4 class="text-sm font-semibold text-blue-900 flex items-center">
                                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs mr-2">
                                                Item {{ index + 1 }}
                                            </span>
                                        </h4>
                                        <CoreActionButton :icon="deleteIcon" text="Remove" color="error"
                                            :click="() => deleteStockItem(index)" size="sm" />
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <!-- Consolidated Stock Item Selection -->
                                        <div class="flex flex-col space-y-2">
                                            <label class="font-medium text-gray-700">Stock Item (with Batch &
                                                Lot)</label>
                                            <CoreDropdown :is-searchable="true" :items="consolidatedStockItems"
                                                :model-value="getConsolidatedItemForRequisition(requisition)"
                                                @update:modelValue="onStockItemSelect(requisition, $event)"
                                                placeholder="Search and select item..." class="text-sm" />
                                            <div v-if="requisition.stock_item && requisition.stock_item.id !== 0"
                                                class="text-xs text-gray-600 bg-gray-100 p-2 rounded">
                                                <div class="flex justify-between">
                                                    <span><strong>Available:</strong></span>
                                                    <span class="font-medium text-green-600">{{
                                                        requisition.stock_item.quantity || 'N/A' }}</span>
                                                </div>
                                                <div v-if="requisition.expiry_date" class="flex justify-between">
                                                    <span><strong>Expires:</strong></span>
                                                    <span class="font-medium">{{ formatDate(requisition.expiry_date)
                                                        }}</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Quantity Input -->
                                        <div class="flex flex-col space-y-2">
                                            <FormKit label="Quantity to Transfer" type="number"
                                                validation="required|min:1" v-model="requisition.quantity_requested"
                                                :max="requisition.stock_item.quantity" placeholder="Enter quantity" />
                                            <div v-if="requisition.stock_item && requisition.stock_item.quantity"
                                                class="text-xs text-gray-500">
                                                Max available: {{ requisition.stock_item.quantity }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Empty State -->
                            <div v-else
                                class="text-center py-16 bg-white rounded-lg border-2 border-dashed border-blue-300">
                                <CubeIcon class="w-20 h-20 text-blue-300 mx-auto mb-4" />
                                <h3 class="text-xl font-semibold text-blue-900 mb-2">No items selected for transfer</h3>
                                <p class="text-blue-600 mb-6 max-w-md mx-auto">
                                    Start by adding stock items with their batch and lot information to create your
                                    transfer request.
                                </p>
                                <CoreActionButton color="primary" :click="addStockItem" text="Add First Item"
                                    :icon="addIcon" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { TrashIcon, PlusIcon, InformationCircleIcon, ClipboardDocumentListIcon, CubeIcon } from '@heroicons/vue/24/solid/index.js';
import type { Department, Request, RequisitionItem, Response, Ward, Page, DropdownItem, Facility, StockItem } from '@/types';
import StockModule from '@/repository/modules/stock';
import { endpoints } from '@/services/endpoints';
import fetchRequest from '@/services/fetch';
import Package from '@/package.json';

definePageMeta({
    layout: 'dashboard'
})

useHead({
    title: `${Package.name.toUpperCase()} - Transfer Stock`
})

const addIcon = PlusIcon
const deleteIcon = TrashIcon
const { $toast, $metadata } = useNuxtApp();
const destinations = ref<DropdownItem[]>([])
const selectedDestination = ref<DropdownItem>({ name: 'select destination' })
const reason = ref<string>('')
const requisitions = ref<RequisitionItem[]>([])
const stockItems = ref<StockItem[]>([])
const cookie = useCookie('token')
const departments = ref<Department[]>([])
const facilities = ref<Facility[]>([])
const wards = ref<Ward[]>([])
const selectedItem = ref<DropdownItem>({ name: 'select item' })
const pages = ref<Page>([
    { name: 'Home', link: '/home' },
    { name: 'Stock Management', link: '#' },
    { name: 'Transactions', link: '/stock-management/transactions' }
])
const requisitionLoading = ref<{ id: number, value: boolean }>({ id: 0, value: false })
const selectedItems: ComputedRef<any[]> = computed(() => {
    const optionsMap = {
        Department: { items: departments, placeholder: 'select department' },
        Facility: { items: facilities, placeholder: 'select facility' },
        Ward: { items: wards, placeholder: 'select ward' }
    } as const;
    const destination = optionsMap[selectedDestination.value.name as keyof typeof optionsMap];
    if (destination) {
        selectedItem.value = { name: destination.placeholder };
        return destination.items.value;
    }
    return [];
});

// Consolidated stock items computed property
const consolidatedStockItems = computed(() => {
    const consolidated: any[] = [];

    stockItems.value.forEach((item: any) => {
        if (item.batches && item.lots) {
            // Create combinations of batches and lots
            item.batches.forEach((batch: string) => {
                item.lots.forEach((lot: string) => {
                    consolidated.push({
                        ...item,
                        name: `${item.name} | Batch: ${batch || 'N/A'} | Lot: ${lot || 'N/A'}`,
                        displayName: item.name,
                        batch_number: batch,
                        lot_number: lot,
                        available_quantity: item.quantity || 0,
                        uniqueKey: `${item.id}-${batch}-${lot}`
                    });
                });
            });
        } else if (item.batches) {
            // Only batches available
            item.batches.forEach((batch: string) => {
                consolidated.push({
                    ...item,
                    name: `${item.name} | Batch: ${batch || 'N/A'} | Lot: N/A`,
                    displayName: item.name,
                    batch_number: batch,
                    lot_number: '',
                    available_quantity: item.quantity || 0,
                    uniqueKey: `${item.id}-${batch}-`
                });
            });
        } else if (item.lots) {
            // Only lots available
            item.lots.forEach((lot: string) => {
                consolidated.push({
                    ...item,
                    name: `${item.name} | Batch: N/A | Lot: ${lot || 'N/A'}`,
                    displayName: item.name,
                    batch_number: '',
                    lot_number: lot,
                    available_quantity: item.quantity || 0,
                    uniqueKey: `${item.id}--${lot}`
                });
            });
        } else {
            // No batch or lot information
            consolidated.push({
                ...item,
                name: `${item.name} | Batch: N/A | Lot: N/A`,
                displayName: item.name,
                batch_number: '',
                lot_number: '',
                available_quantity: item.quantity || 0,
                uniqueKey: `${item.id}--`
            });
        }
    });

    return consolidated;
});

// Enhanced form validation
const canSubmit = computed(() => {
    return requisitions.value.length > 0 &&
        selectedDestination.value.name !== '-- select destination --' &&
        selectedItem.value.name !== '-- select item --' &&
        reason.value.trim() !== '' &&
        requisitions.value.every(req =>
            req.stock_item.id !== 0 &&
            req.quantity_requested > 0 &&
            req.quantity_requested <= (req.stock_item.quantity || 0)
        );
});

async function init(): Promise<void> {
    loadStockItems()
    addDestinations()
}

async function loadStockItems(): Promise<void> {
    const stockModule = new StockModule()
    const { data, error } = await stockModule.getStockItem(String(cookie.value), "metadata=true")
    if (data.value) {
        stockItems.value = data.value
    }
    if (error.value) {
        console.error(error.value)
        $toast.error('Failed to load stock items')
    }
}

async function loadFacilities(): Promise<void> {
    const request: Request = {
        route: endpoints.facility,
        method: "GET",
        token: cookie.value as string
    }
    const { data, error }: Response = await fetchRequest(request)
    if (data.value) {
        facilities.value = data.value.data
    }
    if (error.value) {
        console.error(error.value)
    }
}

async function loadWards(): Promise<void> {
    const request: Request = {
        route: 'encounter_type_facility_section_mappings/facility_sections?encounter_type_id=2',
        method: "GET",
        token: cookie.value as string
    }
    const { data, error }: Response = await fetchRequest(request)
    if (data.value) {
        wards.value = data.value
    }
    if (error.value) {
        console.error(error.value)
    }
}

function addDestinations(): void {
    let destinationList: string[] = ['Facility', 'Ward', 'Department']
    destinations.value = destinationList.map(destination => ({ name: destination }))
}

async function checkStockQuantity(requisition: RequisitionItem): Promise<void> {
    requisitionLoading.value = { id: requisition.id, value: true }
    const stockModule = new StockModule()
    const params = {
        stock_item_id: stockItems.value.find((item) => (item.name == requisition.stock_item.name))?.id || 0,
        quantity: requisition.quantity_requested,
        batch: requisition.batch_number,
        lot: requisition.lot_number
    }
    const { data, error, pending }: Response = await stockModule.checkStockQuantity(cookie.value as string, params)
    requisitionLoading.value = { id: requisition.id, value: pending }
    if (data.value) {
        if (!data.value.deduction_allowed) {
            useNuxtApp().$toast.warning(data.value.message)
            requisition.quantity_requested = 0
        }
        data.value.available_quantity !== null ? (requisition.stock_item.quantity = data.value.available_quantity) : requisition.stock_item.quantity = 0;
        requisitionLoading.value = { id: requisition.id, value: false }
    }
    if (error.value) {
        console.error(error.value)
        requisitionLoading.value = { id: requisition.id, value: false }
        $toast.error("Could not verify stock quantity for given lot and batch, please try again")
    }
}

function addStockItem(): void {
    requisitions.value.push({
        id: Date.now(),
        stock_item: { name: 'select item', id: 0, quantity: 0 },
        quantity_requested: 1,
        batch_number: '',
        lot_number: '',
        expiry_date: ''
    })
}

function deleteStockItem(index: number): void {
    if (index >= 0 && index < requisitions.value.length) {
        requisitions.value.splice(index, 1)
    }
}

// New functions for consolidated stock item selection
function getConsolidatedItemForRequisition(requisition: any): any {
    if (requisition.stock_item && requisition.stock_item.id !== 0) {
        return consolidatedStockItems.value.find(item =>
            item.id === requisition.stock_item.id &&
            item.batch_number === requisition.batch_number &&
            item.lot_number === requisition.lot_number
        ) || { name: '-- select item --', id: 0 };
    }
    return { name: '-- select item --', id: 0 };
}

function onStockItemSelect(requisition: any, selectedItem: any): void {
    if (selectedItem && selectedItem.id) {
        requisition.stock_item = {
            name: selectedItem.displayName,
            id: selectedItem.id,
            quantity: selectedItem.available_quantity
        };
        requisition.batch_number = selectedItem.batch_number || '';
        requisition.lot_number = selectedItem.lot_number || '';
        requisition.expiry_date = selectedItem.expiry_date || '';

        // Check stock quantity for the selected item
        if (requisition.stock_item.id) {
            checkStockQuantity(requisition);
        }
    }
}

function formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    try {
        return new Date(dateString).toLocaleDateString();
    } catch {
        return 'N/A';
    }
}

// Clear form function
function clearForm(): void {
    selectedDestination.value = { name: '-- select destination --' };
    selectedItem.value = { name: '-- select item --' };
    reason.value = '';
    requisitions.value = [];
}

onMounted(() => {
    init()
})

watch(selectedDestination, (value: { name: string }) => {
    const destinations: {
        [key: string]: () => Promise<void>
    } = {
        'Department': (async () => { departments.value = $metadata.departments }),
        'Facility': loadFacilities,
        'Ward': loadWards
    }

    const loadFunction = destinations[value.name]
    if (loadFunction) {
        loadFunction()
    }
}, { deep: true })

watch(requisitions, (oldRequisitions: RequisitionItem[], newRequisitions: RequisitionItem[]) => {
    const oldStockItems = oldRequisitions.map((requisition: RequisitionItem) => requisition.stock_item.name);
    const newStockItems = newRequisitions.map((requisition: RequisitionItem) => requisition.stock_item.name);
    if (oldStockItems !== newStockItems) {
        newRequisitions.forEach((requisition) => {
            requisition.stock_item.id = stockItems.value.find((item) => item.name === requisition.stock_item.name)?.id || 0;
            requisition.stock_item.id && checkStockQuantity(requisition);
        });
    }
}, { deep: true });
</script>
<style scoped></style>