import nitroPublic from "nitro-public-module";

export default defineNuxtConfig({
  typescript: {
    shim: true,
  },

  ssr: false,

  experimental: {
    cookieStore: true
  },

  modules: [
    "@nuxtjs/tailwindcss",
    "@pinia/nuxt",
    "@pinia-plugin-persistedstate/nuxt",
    "@formkit/nuxt"
  ],

  css: [
    "~/assets/css/font.scss",
    "~/assets/css/additional-styles.scss",
  ],

  components: true,

  app: {
    pageTransition: { name: "page", mode: "out-in" },
    layoutTransition: { name: "layout", mode: "out-in" }
  },

  build: {
    transpile: ["@heroicons/vue"],
  },

  runtimeConfig: {
    public: {
      VITEPRESS_URL: process.env.VITEPRESS_URL,
      MACHINE_INTEGRATION_URL: process.env.MACHINE_INTEGRATION_URL,
    },
  },

  nitro: {
    modules: [nitroPublic()]
  },

  compatibilityDate: "2024-12-11"
});